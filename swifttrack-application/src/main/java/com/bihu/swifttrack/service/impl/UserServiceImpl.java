package com.bihu.swifttrack.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.bihu.swifttrack.common.enums.VerifyMethodEnum;
import com.bihu.swifttrack.common.exception.BusinessException;
import com.bihu.swifttrack.common.exception.ServerCode;
import com.bihu.swifttrack.common.utils.BizAssert;
import com.bihu.swifttrack.dto.OrganizationDTO;
import com.bihu.swifttrack.dto.UserInfoDTO;
import com.bihu.swifttrack.mybatis.repository.UserRelationshipsRepository;
import com.bihu.swifttrack.mybatis.repository.UserRepository;
import com.bihu.swifttrack.mybatis.repository.UserRoleRepository;
import com.bihu.swifttrack.mybatis.repository.po.UserPO;
import com.bihu.swifttrack.mybatis.repository.po.UserRelationshipsPO;
import com.bihu.swifttrack.service.OrganizationService;
import com.bihu.swifttrack.service.UserService;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.security.crypto.password.PasswordEncoder;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 * @date 2024/6/4
 */
@AllArgsConstructor
@Service
@Slf4j
public class UserServiceImpl implements UserService {

  private final UserRepository userRepository;
  private final UserRelationshipsRepository userRelationshipsRepository;
  private final UserRoleRepository userRoleRepository;
  private final PasswordEncoder passwordEncoder;
  private final BizAssert bizAssert;
  private final OrganizationService organizationService;

  /**
   * @see UserService#getUserInfo(String)
   */
  @Override
  public UserInfoDTO getUserInfo(String loginName) {
    try {
      UserInfoDTO userInfoDTO = userRoleRepository.getUserRoles(loginName);
      if (userInfoDTO == null) {
        throw new BusinessException(ServerCode.LOGIN_METHOD_ERROR);
      }

      UserPO user = userRepository.getById(userInfoDTO.getUserId());
      if (user == null || (user.getIsDeleted() != null && user.getIsDeleted() == 1)) {
        throw new BusinessException(ServerCode.LOGIN_METHOD_ERROR);
      }

      String verifyMethod = user.getVerifyMethod();
      if (verifyMethod != null) {
        VerifyMethodEnum verifyMethodEnum = VerifyMethodEnum.toVerifyType(verifyMethod);
        switch (verifyMethodEnum) {
          case Phone:
            if (!user.getPhone().equals(loginName)) {
              throw new BusinessException(ServerCode.LOGIN_METHOD_ERROR);
            }
            break;
          case Email:
            if (!user.getEmail().equals(loginName)) {
              throw new BusinessException(ServerCode.LOGIN_METHOD_ERROR);
            }
          default:
            break;
        }
      }

      // 填充组织信息
      if (user.getOrganizationId() != null) {
        userInfoDTO.setOrganization(organizationService.getOrganization(user.getOrganizationId()));
      }

      return userInfoDTO;
    } catch (BusinessException e) {
      log.error("获取用户信息异常，loginName={}", loginName, e);
      throw e;
    } catch (Exception e) {
      log.error("获取用户信息异常，loginName={}", loginName, e);
      throw new BusinessException("获取用户信息异常，loginName=" + loginName);
    }
  }

  /**
   * @see UserService#changePassword(String, String, String)
   */
  @Override
  public void changePassword(String username, String oldPassword, String newPassword) {
    try {
      UserPO user = userRepository.getUserByUsername(username);

      bizAssert.notNull(user, "用户不存在");
      bizAssert.isTrue(passwordEncoder.matches(oldPassword, user.getPassword()), "输入的旧密码错误");

      // 验证新密码复杂度
      if (!isPasswordComplexityValid(newPassword)) {
        throw new BusinessException(ServerCode.PASSWORD_COMPLEXITY_INVALID);
      }

      // 验证新密码是否与当前密码相同
      if (passwordEncoder.matches(newPassword, user.getPassword())) {
        throw new BusinessException(ServerCode.PASSWORD_SAME_AS_CURRENT);
      }

      resetPassword(newPassword, user);
    } catch (BusinessException e) {
      throw e;
    } catch (Exception e) {
      throw new BusinessException("修改密码异常，username" + username);
    }
  }

  @Override
  public Long getParentId(Long operator) {
    UserRelationshipsPO userRelationshipsPO =
        userRelationshipsRepository.getOne(
            new QueryWrapper<UserRelationshipsPO>().eq("user_id", operator));
    bizAssert.notNull(userRelationshipsPO, ServerCode.NOT_EXIST_PARENT_USER.getDesc());
    return userRelationshipsPO.getParentUserId();
  }

  private void resetPassword(String newPassword, UserPO user) {
    user.setPassword(passwordEncoder.encode(newPassword));
    userRepository.update(user, new QueryWrapper<UserPO>().eq("user_id", user.getUserId()));
  }

  /**
   * 验证密码复杂度
   * 要求：至少包含三种字符类型（大写字母、小写字母、数字、特殊字符）且长度不少于8位
   *
   * @param password 待验证的密码
   * @return true if password meets complexity requirements, false otherwise
   */
  private boolean isPasswordComplexityValid(String password) {
    if (password == null || password.length() < 8) {
      return false;
    }

    int typeCount = 0;
    boolean hasUppercase = password.matches(".*[A-Z].*");
    boolean hasLowercase = password.matches(".*[a-z].*");
    boolean hasDigit = password.matches(".*[0-9].*");
    boolean hasSpecialChar = password.matches(".*[!@#$%^&*()_+\\-=\\[\\]{};':\"\\\\|,.<>/?].*");

    if (hasUppercase) typeCount++;
    if (hasLowercase) typeCount++;
    if (hasDigit) typeCount++;
    if (hasSpecialChar) typeCount++;

    return typeCount >= 3;
  }

  @Override
  public boolean isUserEmailExist(String email) {
    return userRepository.getUserByEmail(email) != null;
  }

  @Override
  public boolean isUserPhoneExist(String phone) {
    return userRepository.getUserByPhone(phone) != null;
  }

  @Override
  public Long addUser(UserPO userPO) {
    // 检查新增用户是否已注册
    QueryWrapper<UserPO> query = new QueryWrapper<>();
    query.eq("email", userPO.getEmail()).or().eq("phone", userPO.getPhone());
    UserPO existUser = userRepository.getOne(query);
    if (existUser != null) {
      if (existUser.getPhone().equals(userPO.getPhone())) {
        throw new BusinessException(ServerCode.PHONE_REGISTERED);
      }
      if (existUser.getEmail().equals(userPO.getEmail())) {
        throw new BusinessException(ServerCode.EMAIL_REGISTERED);
      }
      throw new BusinessException(ServerCode.REGISTER_USER_EXIST);
    }

    // 验证密码复杂度
    if (!isPasswordComplexityValid(userPO.getPassword())) {
      throw new BusinessException(ServerCode.PASSWORD_COMPLEXITY_INVALID);
    }

    // 更新 password
    userPO.setPassword(passwordEncoder.encode(userPO.getPassword()));
    userRepository.save(userPO);

    return userPO.getUserId();
  }

  @Override
  public void updateUserOrganization(Long userId, Long organizationId) {
    try {
      // 检查用户是否存在
      UserPO user = userRepository.getById(userId);
      bizAssert.notNull(user, "用户不存在");

      // 检查组织是否存在
      OrganizationDTO organization = organizationService.getOrganization(organizationId);
      bizAssert.notNull(organization, "组织不存在");

      // 更新用户组织
      user.setOrganizationId(organizationId);
      userRepository.update(user, new QueryWrapper<UserPO>().eq("user_id", userId));
    } catch (BusinessException e) {
      throw e;
    } catch (Exception e) {
      log.error("更新用户组织异常，userId={}, organizationId={}", userId, organizationId, e);
      throw new BusinessException("更新用户组织异常");
    }
  }

  @Override
  public OrganizationDTO getUserOrganization(Long userId) {
    try {
      // 检查用户是否存在
      UserPO user = userRepository.getById(userId);
      bizAssert.notNull(user, "用户不存在");

      // 如果用户没有关联组织，返回null
      if (user.getOrganizationId() == null) {
        return null;
      }

      // 获取组织信息
      return organizationService.getOrganization(user.getOrganizationId());
    } catch (BusinessException e) {
      throw e;
    } catch (Exception e) {
      log.error("获取用户组织信息异常，userId={}", userId, e);
      throw new BusinessException("获取用户组织信息异常");
    }
  }

  @Override
  public boolean isUserInOrganization(Long userId, Long organizationId) {
    try {
      // 检查用户是否存在
      UserPO user = userRepository.getById(userId);
      if (user == null) {
        return false;
      }

      // 如果用户没有关联组织，返回false
      if (user.getOrganizationId() == null) {
        return false;
      }

      // 检查是否属于指定组织
      return user.getOrganizationId().equals(organizationId);
    } catch (Exception e) {
      log.error("检查用户组织归属异常，userId={}, organizationId={}", userId, organizationId, e);
      return false;
    }
  }
}
