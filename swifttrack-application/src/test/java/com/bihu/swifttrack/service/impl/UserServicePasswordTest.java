package com.bihu.swifttrack.service.impl;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.*;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.bihu.swifttrack.common.exception.BusinessException;
import com.bihu.swifttrack.common.exception.ServerCode;
import com.bihu.swifttrack.common.utils.BizAssert;
import com.bihu.swifttrack.mybatis.repository.UserRelationshipsRepository;
import com.bihu.swifttrack.mybatis.repository.UserRepository;
import com.bihu.swifttrack.mybatis.repository.UserRoleRepository;
import com.bihu.swifttrack.mybatis.repository.po.UserPO;
import com.bihu.swifttrack.service.OrganizationService;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.security.crypto.password.PasswordEncoder;

@ExtendWith(MockitoExtension.class)
class UserServicePasswordTest {

  @Mock private UserRepository userRepository;
  @Mock private UserRelationshipsRepository userRelationshipsRepository;
  @Mock private UserRoleRepository userRoleRepository;
  @Mock private PasswordEncoder passwordEncoder;
  @Mock private BizAssert bizAssert;
  @Mock private OrganizationService organizationService;

  @InjectMocks private UserServiceImpl userService;

  private UserPO mockUser;

  @BeforeEach
  void setUp() {
    mockUser = new UserPO();
    mockUser.setUserId(1L);
    mockUser.setName("testUser");
    mockUser.setEmail("<EMAIL>");
    mockUser.setPhone("13800138000");
    mockUser.setPassword("encodedOldPassword");
  }

  @Test
  void changePassword_WithValidComplexPassword_ShouldSucceed() {
    // 准备测试数据
    String username = "testuser";
    String oldPassword = "OldPass123!";
    String newPassword = "NewPass456@";

    when(userRepository.getUserByUsername(username)).thenReturn(mockUser);
    when(passwordEncoder.matches(oldPassword, mockUser.getPassword())).thenReturn(true);
    when(passwordEncoder.matches(newPassword, mockUser.getPassword())).thenReturn(false);
    when(passwordEncoder.encode(newPassword)).thenReturn("encodedNewPassword");

    // 执行测试
    assertDoesNotThrow(() -> userService.changePassword(username, oldPassword, newPassword));

    // 验证方法调用
    verify(userRepository).update(any(UserPO.class), any(QueryWrapper.class));
  }

  @Test
  void changePassword_WithInvalidComplexity_ShouldThrowException() {
    // 准备测试数据
    String username = "testuser";
    String oldPassword = "OldPass123!";
    String newPassword = "simple"; // 不符合复杂度要求

    when(userRepository.getUserByUsername(username)).thenReturn(mockUser);
    when(passwordEncoder.matches(oldPassword, mockUser.getPassword())).thenReturn(true);

    // 执行测试并验证异常
    BusinessException exception = assertThrows(
        BusinessException.class, () -> userService.changePassword(username, oldPassword, newPassword));
    
    assertEquals(ServerCode.PASSWORD_COMPLEXITY_INVALID, exception.getServerCode());
  }

  @Test
  void changePassword_WithSamePassword_ShouldThrowException() {
    // 准备测试数据
    String username = "testuser";
    String oldPassword = "OldPass123!";
    String newPassword = "OldPass123!"; // 与旧密码相同

    when(userRepository.getUserByUsername(username)).thenReturn(mockUser);
    when(passwordEncoder.matches(oldPassword, mockUser.getPassword())).thenReturn(true);
    when(passwordEncoder.matches(newPassword, mockUser.getPassword())).thenReturn(true);

    // 执行测试并验证异常
    BusinessException exception = assertThrows(
        BusinessException.class, () -> userService.changePassword(username, oldPassword, newPassword));
    
    assertEquals(ServerCode.PASSWORD_SAME_AS_CURRENT, exception.getServerCode());
  }

  @Test
  void changePassword_WithShortPassword_ShouldThrowException() {
    // 准备测试数据
    String username = "testuser";
    String oldPassword = "OldPass123!";
    String newPassword = "Abc1@"; // 少于8位

    when(userRepository.getUserByUsername(username)).thenReturn(mockUser);
    when(passwordEncoder.matches(oldPassword, mockUser.getPassword())).thenReturn(true);

    // 执行测试并验证异常
    BusinessException exception = assertThrows(
        BusinessException.class, () -> userService.changePassword(username, oldPassword, newPassword));
    
    assertEquals(ServerCode.PASSWORD_COMPLEXITY_INVALID, exception.getServerCode());
  }

  @Test
  void changePassword_WithOnlyTwoCharacterTypes_ShouldThrowException() {
    // 准备测试数据
    String username = "testuser";
    String oldPassword = "OldPass123!";
    String newPassword = "abcdefgh"; // 只有小写字母，不符合复杂度要求

    when(userRepository.getUserByUsername(username)).thenReturn(mockUser);
    when(passwordEncoder.matches(oldPassword, mockUser.getPassword())).thenReturn(true);

    // 执行测试并验证异常
    BusinessException exception = assertThrows(
        BusinessException.class, () -> userService.changePassword(username, oldPassword, newPassword));
    
    assertEquals(ServerCode.PASSWORD_COMPLEXITY_INVALID, exception.getServerCode());
  }

  @Test
  void addUser_WithInvalidPasswordComplexity_ShouldThrowException() {
    // 准备测试数据
    UserPO userPO = new UserPO();
    userPO.setEmail("<EMAIL>");
    userPO.setPhone("***********");
    userPO.setPassword("simple"); // 不符合复杂度要求

    when(userRepository.getOne(any(QueryWrapper.class))).thenReturn(null);

    // 执行测试并验证异常
    BusinessException exception = assertThrows(
        BusinessException.class, () -> userService.addUser(userPO));
    
    assertEquals(ServerCode.PASSWORD_COMPLEXITY_INVALID, exception.getServerCode());
  }

  @Test
  void addUser_WithValidPasswordComplexity_ShouldSucceed() {
    // 准备测试数据
    UserPO userPO = new UserPO();
    userPO.setEmail("<EMAIL>");
    userPO.setPhone("***********");
    userPO.setPassword("ValidPass123!"); // 符合复杂度要求

    when(userRepository.getOne(any(QueryWrapper.class))).thenReturn(null);
    when(passwordEncoder.encode("ValidPass123!")).thenReturn("encodedPassword");

    // 执行测试
    assertDoesNotThrow(() -> userService.addUser(userPO));

    // 验证方法调用
    verify(userRepository).save(userPO);
    verify(passwordEncoder).encode("ValidPass123!");
  }
}
