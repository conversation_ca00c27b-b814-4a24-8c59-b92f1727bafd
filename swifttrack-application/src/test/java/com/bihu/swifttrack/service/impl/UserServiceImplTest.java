package com.bihu.swifttrack.service.impl;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.*;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.bihu.swifttrack.common.exception.BusinessException;
import com.bihu.swifttrack.common.exception.ServerCode;
import com.bihu.swifttrack.common.utils.BizAssert;
import com.bihu.swifttrack.dto.UserInfoDTO;
import com.bihu.swifttrack.mybatis.repository.UserRelationshipsRepository;
import com.bihu.swifttrack.mybatis.repository.UserRepository;
import com.bihu.swifttrack.mybatis.repository.UserRoleRepository;
import com.bihu.swifttrack.mybatis.repository.po.UserPO;
import com.bihu.swifttrack.service.OrganizationService;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.security.crypto.password.PasswordEncoder;

@ExtendWith(MockitoExtension.class)
class UserServiceImplTest {

  @Mock private UserRepository userRepository;

  @Mock private UserRelationshipsRepository userRelationshipsRepository;

  @Mock private UserRoleRepository userRoleRepository;

  @Mock private PasswordEncoder passwordEncoder;

  @Mock private BizAssert bizAssert;

  @Mock private OrganizationService organizationService;

  @InjectMocks private UserServiceImpl userService;

  private UserPO mockUser;
  private UserInfoDTO mockUserInfoDTO;

  @BeforeEach
  void setUp() {
    // 准备测试数据
    mockUser = new UserPO();
    mockUser.setUserId(1L);
    mockUser.setName("testUser");
    mockUser.setEmail("<EMAIL>");
    mockUser.setPhone("13800138000");
    mockUser.setOrganizationId(100L);
    mockUser.setPassword("encodedPassword");
    mockUser.setVerifyMethod("email");
    mockUser.setIsDeleted(0);

    mockUserInfoDTO = new UserInfoDTO();
    mockUserInfoDTO.setUserId(1L);
    mockUserInfoDTO.setEmail("<EMAIL>");
    mockUserInfoDTO.setPhone("13800138000");

    mockOrganizationDTO = new OrganizationDTO();
    mockOrganizationDTO.setId(100L);
    mockOrganizationDTO.setName("Test Organization");
    mockOrganizationDTO.setType(0); // 企业类型
    mockOrganizationDTO.setCode("TEST_ORG");
  }

  @Test
  void getUserInfo_WithValidEmail_ShouldReturnUserInfo() {
    // 准备测试数据
    String email = "<EMAIL>";
    when(userRoleRepository.getUserRoles(email)).thenReturn(mockUserInfoDTO);
    when(userRepository.getById(1L)).thenReturn(mockUser);
    when(organizationService.getOrganization(100L)).thenReturn(mockOrganizationDTO);

    // 执行测试
    UserInfoDTO result = userService.getUserInfo(email);

    // 验证结果
    assertNotNull(result);
    assertEquals(mockUserInfoDTO.getUserId(), result.getUserId());
    assertEquals(mockUserInfoDTO.getEmail(), result.getEmail());
    assertEquals(mockUserInfoDTO.getPhone(), result.getPhone());
    assertNotNull(result.getOrganization());
    assertEquals(mockOrganizationDTO.getId(), result.getOrganization().getId());
    assertEquals(mockOrganizationDTO.getName(), result.getOrganization().getName());
  }

  @Test
  void updateUserOrganization_WithValidData_ShouldUpdateSuccessfully() {
    // 准备测试数据
    Long userId = 1L;
    Long organizationId = 100L;
    when(userRepository.getById(userId)).thenReturn(mockUser);
    when(organizationService.getOrganization(organizationId)).thenReturn(mockOrganizationDTO);

    // 执行测试
    userService.updateUserOrganization(userId, organizationId);

    // 验证调用
    verify(userRepository).update(any(UserPO.class), any());
    verify(organizationService).getOrganization(organizationId);
  }

  @Test
  void getUserOrganization_WithValidUserId_ShouldReturnOrganization() {
    // 准备测试数据
    Long userId = 1L;
    when(userRepository.getById(userId)).thenReturn(mockUser);
    when(organizationService.getOrganization(100L)).thenReturn(mockOrganizationDTO);

    // 执行测试
    OrganizationDTO result = userService.getUserOrganization(userId);

    // 验证结果
    assertNotNull(result);
    assertEquals(mockOrganizationDTO.getId(), result.getId());
    assertEquals(mockOrganizationDTO.getName(), result.getName());
    assertEquals(mockOrganizationDTO.getType(), result.getType());
    assertEquals(mockOrganizationDTO.getCode(), result.getCode());
  }

  @Test
  void isUserInOrganization_WithValidData_ShouldReturnTrue() {
    // 准备测试数据
    Long userId = 1L;
    Long organizationId = 100L;
    when(userRepository.getById(userId)).thenReturn(mockUser);

    // 执行测试
    boolean result = userService.isUserInOrganization(userId, organizationId);

    // 验证结果
    assertTrue(result);
  }

  @Test
  void isUserInOrganization_WithDifferentOrganization_ShouldReturnFalse() {
    // 准备测试数据
    Long userId = 1L;
    Long organizationId = 200L;
    when(userRepository.getById(userId)).thenReturn(mockUser);

    // 执行测试
    boolean result = userService.isUserInOrganization(userId, organizationId);

    // 验证结果
    assertFalse(result);
  }

  @Test
  void getUserInfo_WithNonExistentUser_ShouldThrowException() {
    // 准备测试数据
    String email = "<EMAIL>";
    when(userRoleRepository.getUserRoles(email)).thenReturn(null);

    // 执行测试并验证异常
    assertThrows(BusinessException.class, () -> userService.getUserInfo(email));
  }

  @Test
  void updateUserOrganization_WithNonExistentUser_ShouldThrowException() {
    // 准备测试数据
    Long userId = 999L;
    Long organizationId = 100L;
    when(userRepository.getById(userId)).thenReturn(null);

    // 执行测试并验证异常
    assertThrows(
        BusinessException.class, () -> userService.updateUserOrganization(userId, organizationId));
  }

  @Test
  void changePassword_WithValidComplexPassword_ShouldSucceed() {
    // 准备测试数据
    String username = "testuser";
    String oldPassword = "OldPass123!";
    String newPassword = "NewPass456@";
    UserPO user = new UserPO();
    user.setUserId(1L);
    user.setPassword("encodedOldPassword");

    when(userRepository.getUserByUsername(username)).thenReturn(user);
    when(passwordEncoder.matches(oldPassword, user.getPassword())).thenReturn(true);
    when(passwordEncoder.matches(newPassword, user.getPassword())).thenReturn(false);
    when(passwordEncoder.encode(newPassword)).thenReturn("encodedNewPassword");

    // 执行测试
    assertDoesNotThrow(() -> userService.changePassword(username, oldPassword, newPassword));

    // 验证方法调用
    verify(userRepository).update(any(UserPO.class), any(QueryWrapper.class));
  }

  @Test
  void changePassword_WithInvalidComplexity_ShouldThrowException() {
    // 准备测试数据
    String username = "testuser";
    String oldPassword = "OldPass123!";
    String newPassword = "simple"; // 不符合复杂度要求
    UserPO user = new UserPO();
    user.setUserId(1L);
    user.setPassword("encodedOldPassword");

    when(userRepository.getUserByUsername(username)).thenReturn(user);
    when(passwordEncoder.matches(oldPassword, user.getPassword())).thenReturn(true);

    // 执行测试并验证异常
    BusinessException exception = assertThrows(
        BusinessException.class, () -> userService.changePassword(username, oldPassword, newPassword));

    assertEquals(ServerCode.PASSWORD_COMPLEXITY_INVALID, exception.getServerCode());
  }

  @Test
  void changePassword_WithSamePassword_ShouldThrowException() {
    // 准备测试数据
    String username = "testuser";
    String oldPassword = "OldPass123!";
    String newPassword = "OldPass123!"; // 与旧密码相同
    UserPO user = new UserPO();
    user.setUserId(1L);
    user.setPassword("encodedOldPassword");

    when(userRepository.getUserByUsername(username)).thenReturn(user);
    when(passwordEncoder.matches(oldPassword, user.getPassword())).thenReturn(true);
    when(passwordEncoder.matches(newPassword, user.getPassword())).thenReturn(true);

    // 执行测试并验证异常
    BusinessException exception = assertThrows(
        BusinessException.class, () -> userService.changePassword(username, oldPassword, newPassword));

    assertEquals(ServerCode.PASSWORD_SAME_AS_CURRENT, exception.getServerCode());
  }

  @Test
  void updateUserOrganization_WithNonExistentOrganization_ShouldThrowException() {
    // 准备测试数据
    Long userId = 1L;
    Long organizationId = 999L;
    when(userRepository.getById(userId)).thenReturn(mockUser);
    when(organizationService.getOrganization(organizationId)).thenReturn(null);
    doThrow(new BusinessException("组织不存在")).when(bizAssert).notNull(null, "组织不存在");

    // 执行测试并验证异常
    assertThrows(
        BusinessException.class, () -> userService.updateUserOrganization(userId, organizationId));
  }
}
